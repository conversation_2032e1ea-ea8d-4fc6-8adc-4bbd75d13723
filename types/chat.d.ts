interface IContent {
  type: 'TEXT' | 'IMAGE';
  text?: string;
  mime?: string;
  data?: string;
  name?: string;
}
interface IUtterance {
  type: 'TEXT' | 'IMAGE' | 'AUDIO'; // 枚举值：TEXT、IMAGE、AUDIO
  content: string; // 文本内容或图片URL
}
interface IAsrParams {
  sessionId: string; // sessionId
  format: string; // 格式，pcm
  sampleRate: number; // 采样率，常见有8000、16000
  index: number; // 数据包序号，从1开始，以-1*Seq结束
  data: Blob | ArrayBuffer | null; // 二进制的音频数据
}
interface IAsrRequest {
  endTime: number;
  resIndex: number;
  sessionFinished: boolean;
  sessionId: string;
  speechTime: number;
  startTime: number;
  status: number;
  subSessionId: string;
  text: string;
  full_text: string;
}
interface ITopicRecommend {
  desc: string;
  icon: string;
  prompt: string;
  title: string;
}
// 新建会话接口入参
interface ICreateConversationRequest {
  user_id: string;
}

// 新建会话接口出参（直接的业务数据格式）
interface ICreateConversationResponse {
  success: boolean;
  conversation_id: string; // 格式: "user123_2024-01-01-10-30-45"
}

interface IConversation {
  conversationId: string;
  creatorMis: string;
  creatorName: string;
  title: string;
  model: string;
  messageCount: number | null;
  status: number;
  agentId: string;
  addTime: string | null;
  updateTime: string | null;
  appfactoryConversationId: string | null;
  conversationType: string | null;
  isEncryptionMode: boolean;
  agentInfo: IAgentInfo;
  welcomeMessageSwitch: boolean;
}

interface IAgentInfo {
  agentId: string;
  agentName: string;
  agentIconUrl: string;
  systemPrompt: string;
  promptMap: Record<string, string>;
  welcomeMessage: string;
  pluginList: string[];
  modelList: IModel[];
  modelNameList: string[];
  modelFileUploadButton: string[];
  defaultPlugin: string;
  defaultModel: string;
  description: string;
  sort: number;
  safeModeSupported: boolean;
  securityModelConfig: ISecurityModelConfig;
  fileUploadButton: boolean;
  generalAgentTypeEnum: string;
  fileUploadConfig: IFileUploadConfig;
}

interface IModel {
  label: string;
  model: string;
  description: string;
  maxTokenCount: number;
  imageUrl: string;
}

interface ISecurityModelConfig {
  pluginList: string[];
  modelNameList: string[];
  modelList: Model[];
  defaultModel: string;
  fileUploadSupported: boolean;
  safeAgentIconUrl: string;
}

interface IFileUploadConfig {
  format: string[];
  limitSize: number;
}
// 对话接口入参
interface IChatRequest {
  content: string; // 用户问题
  conversation_id: string; // 对话 ID，用于上下文关联
  user_id: string; // 用户ID
}
interface IChatMessage {
  id: string;
  created?: number;
  choices?: Choice[];
  content?: string;
  agentInfo?: AgentInfo;
  loadingStatus?: boolean;
  lastOne?: boolean;
  reasoning_content?: string;
  reasoning_status?: string;
  pre_tools?: any[];
  accumulated_content?: string;
  accumulated_reasoning_content?: string;
  type?: string; // 添加type字段，用于识别消息类型（如system_info）
}

interface IChoice {
  delta: unknown;
  index: number;
  finish_reason: string;
}

interface IChatSate {
  conversationInfo: IConversation;
  answerStatus: AnswerStatusEnum;
}
type Role = 'user' | 'assistant' | 'system';

interface IReasoningData {
  content: string;
  status: string;
}
interface IChatStreamContent {
  role: Role;
  content: string;
  key: number | string;
  isFinish: boolean;
  debugInfo?: IChatMessage;
  reasoningData: IReasoningData;
  pre_tools?: any[];
  isToolCallLoading?: boolean;
}
