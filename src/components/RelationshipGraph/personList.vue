<template>
  <div class="person-list-container">
    <!-- 头部标题 -->
    <div class="person-header"></div>

    <!-- 搜索输入框 -->
    <div v-if="showSearchInput" class="search-container">
      <div class="search-input-wrapper">
        <input
          v-model="searchQuery"
          type="text"
          class="search-input"
          placeholder="请输入姓名进行搜索"
          maxlength="50"
          @keyup.enter="handleSearch"
          @input="onSearchInput"
        />
        <button class="search-confirm-btn" :disabled="!searchQuery.trim() || isSearching" @click="handleSearch">
          {{ isSearching ? '搜索中...' : '搜索' }}
        </button>
        <button class="search-clear-btn" @click="clearSearch">清除</button>
      </div>
    </div>

    <!-- 人物列表 -->
    <div class="person-content">
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>

      <div v-else-if="persons.length === 0" class="empty-container">
        <div class="empty-text">暂无人际关系记录</div>
        <div class="add-person-hint">点击下方按钮添加新的人际关系</div>
      </div>

      <div v-else class="person-items">
        <div v-for="person in persons" :key="person.person_id" class="person-item">
          <div class="person-info">
            <div class="person-name-row">
              <span class="person-name">{{ person.canonical_name }}</span>
              <span v-if="getKeyAttribute(person, '关系')" class="person-relation">{{
                getKeyAttribute(person, '关系')
              }}</span>
            </div>
            <div
              v-if="person.profile_summary"
              class="person-summary"
              :class="{ expanded: expandedSummaries.includes(person.person_id) }"
              @mousedown="handleMouseDown"
              @mouseup="(event) => handleMouseUp(event, person.person_id)"
            >
              {{ person.profile_summary }}
            </div>
          </div>
          <div class="person-actions">
            <button class="edit-btn" title="编辑" @click.stop="handleEditPerson(person)">
              <img src="@/assets/icon/edit.png" alt="编辑" class="edit-icon" />
            </button>
            <button class="delete-btn" title="删除" @click.stop="handleDeletePerson(person)">
              <img src="@/assets/icon/delete.png" alt="删除" class="delete-icon" />
            </button>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="person-footer">
        <button class="add-person-btn" @click="showAddPersonDialog = true">
          <span class="add-icon">+</span>
          添加新的人际关系
        </button>
      </div>
    </div>

    <!-- 添加人员对话框 -->
    <div v-if="showAddPersonDialog" class="dialog-overlay">
      <div class="dialog-container edit-dialog">
        <div class="dialog-header">
          <div class="dialog-title">添加新的人际关系</div>
          <div class="dialog-close" @click="closeAddPersonDialog">
            <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
          </div>
        </div>
        <div class="dialog-content edit-content">
          <!-- 头像和正式姓名组合区域 -->
          <div class="input-group avatar-name-group">
            <!-- 标签行 -->
            <div class="labels-row">
              <label class="input-label avatar-label">头像</label>
              <label class="input-label name-label">正式姓名 *</label>
            </div>
            <!-- 内容行 -->
            <div class="content-row">
              <div class="avatar-section">
                <img
                  :src="getAvatarUrl(addForm.avatar)"
                  alt="头像"
                  class="person-avatar clickable-avatar"
                  @click="handleAddFormAvatarClick"
                />
              </div>
              <div class="name-section">
                <input
                  v-model="addForm.canonical_name"
                  type="text"
                  class="input-field"
                  placeholder="请输入正式姓名"
                  maxlength="50"
                />
              </div>
            </div>
          </div>
          <div class="input-group">
            <label class="input-label">别名</label>
            <input v-model="addForm.aliases" type="text" class="input-field" placeholder="请输入别名" maxlength="100" />
          </div>
          <div class="input-group">
            <label class="input-label">个人简介</label>
            <textarea
              v-model="addForm.profile_summary"
              class="textarea-field"
              placeholder="请输入个人简介"
              rows="3"
              maxlength="500"
            ></textarea>
          </div>
          <div class="input-group">
            <label class="input-label">关键属性</label>
            <div class="key-attributes-container">
              <div
                v-for="(_value, key) in addForm.key_attributes"
                v-show="key && key.trim()"
                :key="key"
                class="attribute-item"
              >
                <input :value="key" type="text" class="attribute-key" :placeholder="key" readonly />
                <input
                  v-model="addForm.key_attributes[key]"
                  type="text"
                  class="attribute-value"
                  :placeholder="`请输入${key}`"
                  maxlength="100"
                />
              </div>
              <!-- 新属性输入框 -->
              <div v-for="attr in newAddAttributes" :key="attr.id" class="add-attribute-container">
                <input v-model="attr.key" type="text" class="attribute-key" placeholder="属性名称" maxlength="20" />
                <input v-model="attr.value" type="text" class="attribute-value" placeholder="属性值" maxlength="100" />
                <button class="remove-attribute-btn" @click="cancelAddAddAttribute(attr.id)">×</button>
              </div>
              <!-- 添加属性按钮 -->
              <div class="add-attribute-btn-container">
                <button class="add-attribute-btn" @click="showAddFormAttributeInput">+ 添加属性</button>
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn" @click="closeAddPersonDialog">取消</button>
          <button class="confirm-btn" :disabled="!addForm.canonical_name.trim() || isAdding" @click="handleAddPerson">
            {{ isAdding ? '添加中...' : '确认添加' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="showDeleteDialog" class="dialog-overlay">
      <div class="dialog-container">
        <div class="dialog-header">
          <div class="dialog-title">确认删除</div>
          <div class="dialog-close" @click="closeDeleteDialog">
            <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
          </div>
        </div>
        <div class="dialog-content">
          <div class="delete-warning">
            确定要删除 <strong>{{ personToDelete?.canonical_name }}</strong> 吗？
          </div>
          <div class="delete-hint">删除后将无法恢复相关的记忆数据</div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn" @click="closeDeleteDialog">取消</button>
          <button class="delete-confirm-btn" :disabled="isDeleting" @click="confirmDeletePerson">
            {{ isDeleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑人员对话框 -->
    <PersonEditDialog
      v-if="showEditDialog"
      :person="personToEdit"
      :user-id="props.userId"
      @close="closeEditDialog"
      @success="handleEditSuccess"
    />

    <!-- 头像选择弹窗 -->
    <AvatarSelectionDialog
      v-if="showAvatarSelectionDialog"
      @close="showAvatarSelectionDialog = false"
      @select-avatar="handleAvatarSelect"
      @upload-avatar="handleUploadAvatar"
    />

    <!-- 隐藏的头像上传组件 -->
    <div style="display: none">
      <AvatarUpload
        ref="avatarUploadRef"
        v-model="hiddenAvatarValue"
        :size="50"
        placeholder="上传头像"
        :max-size="10"
        @upload-success="handleAvatarUploadSuccess"
        @upload-error="handleAvatarUploadError"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getPersons, addPerson, deletePerson, searchPerson, type IPersonData } from '@/apis/relation';
import { showFailToast, showSuccessToast } from 'vant';
import AvatarUpload from '@/components/AvatarUpload.vue';
import AvatarSelectionDialog from '@/components/AvatarSelectionDialog.vue';
import { getAvatarUrl } from '@/utils/avatarUtils';
import PersonEditDialog from './PersonEditDialog.vue';

// Props定义
interface IProps {
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  refresh: [];
}>();

// 响应式数据
const loading = ref(true);
const persons = ref<IPersonData[]>([]);
const showAddPersonDialog = ref(false);
const showDeleteDialog = ref(false);
const showEditDialog = ref(false);
const showAvatarSelectionDialog = ref(false);
const isAdding = ref(false);
const isDeleting = ref(false);
const personToDelete = ref<IPersonData | null>(null);
const personToEdit = ref<IPersonData | null>(null);
const expandedSummaries = ref<string[]>([]);

// 搜索相关数据
const showSearchInput = ref(true);
const searchQuery = ref('');
const isSearching = ref(false);
const isSearchMode = ref(false); // 标记当前是否在搜索模式

// 添加表单数据
const addForm = ref({
  canonical_name: '',
  aliases: '',
  relationships: [] as string[],
  profile_summary: '',
  key_attributes: {} as Record<string, string>,
  avatar: '',
});

// 添加表单新属性相关数据 - 支持多个新属性输入框
const newAddAttributes = ref<Array<{ id: string; key: string; value: string }>>([]);

// 头像上传组件引用
const avatarUploadRef = ref();
const hiddenAvatarValue = ref('');

// 安全地获取key_attributes中的属性值
const getKeyAttribute = (person: IPersonData, key: string): string => {
  if (!person.key_attributes) return '';

  if (typeof person.key_attributes === 'string') {
    try {
      const parsed = JSON.parse(person.key_attributes);
      return parsed[key] || '';
    } catch {
      return '';
    }
  }

  // 由于key_attributes已经被扁平化，直接获取值
  const value = person.key_attributes[key];
  return value && String(value).trim() ? String(value) : '';
};

// 获取人员数据
const loadPersons = async () => {
  try {
    loading.value = true;
    console.log('🔄 [personList.vue] 开始获取人员数据...', {
      userId: props.userId,
    });

    const response = await getPersons({
      userId: props.userId,
      limit: 100,
      offset: 0,
    });

    console.log('📡 [personList.vue] 人员数据响应:', response);

    if (response && response.result === 'success' && response.persons) {
      // 过滤掉用户自己
      persons.value = response.persons.filter((person) => !person.is_user);
      console.log('✅ [personList.vue] 人员数据加载成功，共', persons.value.length, '个人员');
    } else {
      console.warn('⚠️ [personList.vue] 人员数据格式异常:', response);
      persons.value = [];
    }
  } catch (error) {
    console.error('❌ [personList.vue] 获取人员数据失败:', error);
    showFailToast('获取人员数据失败');
    persons.value = [];
  } finally {
    loading.value = false;
  }
};

// 处理添加表单头像点击
const handleAddFormAvatarClick = () => {
  console.log('添加表单头像点击，显示头像选择弹窗');
  showAvatarSelectionDialog.value = true;
};

// 处理头像选择
const handleAvatarSelect = (selectedAvatarId: string) => {
  console.log('✅ [personList.vue] 选择头像:', selectedAvatarId);
  addForm.value.avatar = selectedAvatarId;
  showAvatarSelectionDialog.value = false;
};

// 处理上传头像
const handleUploadAvatar = () => {
  showAvatarSelectionDialog.value = false;
  // 触发隐藏的AvatarUpload组件的上传功能
  if (avatarUploadRef.value && avatarUploadRef.value.triggerUpload) {
    avatarUploadRef.value.triggerUpload();
  }
};

// 处理头像上传成功
const handleAvatarUploadSuccess = (url: string) => {
  console.log('✅ [personList.vue] 头像上传成功:', url);
  addForm.value.avatar = url;
};

// 处理头像上传失败
const handleAvatarUploadError = (error: string) => {
  console.error('❌ [personList.vue] 头像上传失败:', error);
};

// 关闭添加人员对话框
const closeAddPersonDialog = () => {
  showAddPersonDialog.value = false;
  // 重置添加表单
  addForm.value = {
    canonical_name: '',
    aliases: '',
    relationships: [],
    profile_summary: '',
    key_attributes: {},
    avatar: '',
  };
  // 重置添加表单的属性输入
  newAddAttributes.value = [];
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  personToDelete.value = null;
};

// 关闭编辑对话框
const closeEditDialog = () => {
  showEditDialog.value = false;
  personToEdit.value = null;
};

// 生成唯一ID
const generateId = () => {
  return Date.now().toString() + Math.random().toString(36).substring(2, 11);
};

// 显示添加表单新属性输入框
const showAddFormAttributeInput = () => {
  newAddAttributes.value.push({
    id: generateId(),
    key: '',
    value: '',
  });
};

// 取消添加表单新属性
const cancelAddAddAttribute = (id: string) => {
  newAddAttributes.value = newAddAttributes.value.filter((a) => a.id !== id);
};

// 处理添加人员
const handleAddPerson = async () => {
  if (!addForm.value.canonical_name.trim()) {
    showFailToast('请输入正式姓名');
    return;
  }

  try {
    isAdding.value = true;
    console.log('🔄 [personList.vue] 开始添加人员...', {
      userId: props.userId,
      addForm: addForm.value,
    });

    // 合并新属性到key_attributes
    const mergedKeyAttributes = { ...addForm.value.key_attributes };
    newAddAttributes.value.forEach((attr) => {
      // 只有当 attribute-key 不为空时才保存新增的属性
      // 当 attribute-key 为空，attribute-value 不为空时，不保存
      // 当 attribute-key 不为空，attribute-value 为空时，保存
      if (attr.key.trim()) {
        mergedKeyAttributes[attr.key.trim()] = attr.value.trim();
      }
    });

    const response = await addPerson({
      user_id: props.userId,
      canonical_name: addForm.value.canonical_name.trim(),
      aliases: addForm.value.aliases.trim(),
      relationships: addForm.value.relationships,
      profile_summary: addForm.value.profile_summary.trim(),
      key_attributes: mergedKeyAttributes,
      avatar: addForm.value.avatar,
      is_user: false,
    });

    console.log('📡 [personList.vue] 添加人员响应:', response);

    if (response && response.result === 'success') {
      console.log('✅ [personList.vue] 人员添加成功，person_id:', response.person_id);
      showSuccessToast(`快来和小美聊聊生成与${addForm.value.canonical_name.trim()}的专属回忆吧～`);

      // 关闭对话框
      closeAddPersonDialog();

      // 刷新人员列表
      await loadPersons();

      // 通知父组件刷新关系图
      emit('refresh');
    } else {
      console.warn('⚠️ [personList.vue] 添加人员失败:', response);
      showFailToast('添加人员失败');
    }
  } catch (error) {
    console.error('❌ [personList.vue] 添加人员失败:', error);
    showFailToast('添加人员失败');
  } finally {
    isAdding.value = false;
  }
};

// 处理编辑人员
const handleEditPerson = (person: IPersonData) => {
  personToEdit.value = person;
  showEditDialog.value = true;
};

// 处理编辑成功
const handleEditSuccess = async () => {
  // 刷新人员列表
  await loadPersons();

  // 通知父组件刷新关系图
  emit('refresh');

  // 关闭编辑对话框
  closeEditDialog();
};

// 处理删除人员
const handleDeletePerson = (person: IPersonData) => {
  personToDelete.value = person;
  showDeleteDialog.value = true;
};

// 确认删除人员
const confirmDeletePerson = async () => {
  if (!personToDelete.value) return;

  try {
    isDeleting.value = true;
    console.log('🔄 [personList.vue] 开始删除人员...', {
      userId: props.userId,
      personId: personToDelete.value.person_id,
      name: personToDelete.value.canonical_name,
    });

    const response = await deletePerson(props.userId, personToDelete.value.person_id);

    console.log('📡 [personList.vue] 删除人员响应:', response);

    if (response && response.result === 'success') {
      console.log('✅ [personList.vue] 人员删除成功');
      showSuccessToast('删除成功');

      // 关闭对话框
      closeDeleteDialog();

      // 刷新人员列表
      await loadPersons();

      // 通知父组件刷新关系图
      emit('refresh');
    } else {
      console.warn('⚠️ [personList.vue] 删除人员失败:', response);
      showFailToast('删除人员失败');
    }
  } catch (error) {
    console.error('❌ [personList.vue] 删除人员失败:', error);
    showFailToast('删除人员失败');
  } finally {
    isDeleting.value = false;
  }
};

// 鼠标按下时的位置记录
const mouseDownPosition = { x: 0, y: 0 };

// 处理鼠标按下事件
const handleMouseDown = (event: MouseEvent) => {
  mouseDownPosition.x = event.clientX;
  mouseDownPosition.y = event.clientY;
};

// 处理鼠标松开事件
const handleMouseUp = (event: MouseEvent, personId: string) => {
  // 计算鼠标移动距离
  const deltaX = Math.abs(event.clientX - mouseDownPosition.x);
  const deltaY = Math.abs(event.clientY - mouseDownPosition.y);

  // 如果鼠标移动距离很小（小于5像素），并且没有选中文本，则认为是点击操作
  const isClick = deltaX < 5 && deltaY < 5;
  const selection = window.getSelection();
  const hasSelection = selection ? selection.toString().length > 0 : false;

  if (isClick && !hasSelection) {
    toggleSummary(personId);
  }
};

// 切换summary展开状态
const toggleSummary = (personId: string) => {
  const index = expandedSummaries.value.indexOf(personId);
  if (index > -1) {
    expandedSummaries.value.splice(index, 1);
  } else {
    expandedSummaries.value.push(personId);
  }
};

// 处理搜索输入
const onSearchInput = () => {
  // 可以在这里添加实时搜索逻辑，目前保持简单
};

// 执行搜索
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    showFailToast('请输入搜索内容');
    return;
  }

  try {
    isSearching.value = true;
    console.log('🔄 [personList.vue] 开始搜索人员...', {
      userId: props.userId,
      name: searchQuery.value.trim(),
    });

    const response = await searchPerson({
      user_id: props.userId,
      name: searchQuery.value.trim(),
      limit: 10,
    });

    console.log('📡 [personList.vue] 搜索人员响应:', response);

    if (response && response.result === 'success') {
      if (response.persons && response.persons.length > 0) {
        // 过滤掉用户自己
        persons.value = response.persons.filter((person) => !person.is_user);
        isSearchMode.value = true;
        console.log('✅ [personList.vue] 搜索成功，找到', persons.value.length, '个人员');
        showSuccessToast(`找到 ${persons.value.length} 个相关人员`);
      } else {
        persons.value = [];
        isSearchMode.value = true;
        console.log('⚠️ [personList.vue] 搜索成功但无结果');
        showSuccessToast('未找到相关人员');
      }
    } else {
      console.warn('⚠️ [personList.vue] 搜索失败:', response);
      showFailToast('搜索失败');
    }
  } catch (error) {
    console.error('❌ [personList.vue] 搜索人员失败:', error);
    showFailToast('搜索失败');
  } finally {
    isSearching.value = false;
  }
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = '';
  isSearchMode.value = false;
  showSearchInput.value = false;
  // 重新加载所有人员数据
  void loadPersons();
};

// 组件挂载时加载数据
onMounted(() => {
  void loadPersons();
});
</script>

<style lang="scss" scoped>
.person-list-container {
  background: transparent;
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.person-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;
}

.search-container {
  margin-bottom: 18px;
  padding-bottom: 18px;

  .search-input-wrapper {
    display: flex;
    gap: 20px;
    align-items: center;

    .search-input {
      flex: 1;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 16px;
      padding: 16px 20px;
      color: rgba(255, 255, 255, 0.95);
      font-size: 22px;
      box-sizing: border-box;
      transition: all 0.2s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
      }
    }

    .search-confirm-btn,
    .search-clear-btn {
      padding: 16px 24px;
      border-radius: 20px;
      font-size: 22px;
      font-weight: 600;
      cursor: pointer;
      border: 2px solid;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .search-confirm-btn {
      color: #00bcd4;
      border-color: #00bcd4;
      background: rgba(0, 188, 212, 0.15);

      &:hover:not(:disabled) {
        background: rgba(0, 188, 212, 0.25);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
      }
    }

    .search-clear-btn {
      color: #ef4444;
      border-color: #ef4444;
      background: rgba(239, 68, 68, 0.15);

      &:hover:not(:disabled) {
        background: rgba(239, 68, 68, 0.25);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
      }
    }
  }
}

.person-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50px 0;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .loading-text {
    color: #ffffff;
    font-size: 32px;
    font-weight: 600;
    line-height: 1.4;
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
  text-align: center;

  .empty-text {
    color: #ffffff;
    font-size: 32px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 12px;
  }

  .add-person-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 22px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.person-items {
  display: flex;
  flex-direction: column;
  gap: 28px;

  .person-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border: none;
    border-radius: 16px;
    background: rgba(0, 188, 212, 0.05);
    backdrop-filter: blur(10px);
    border-left: 4px solid #00ffff;
    box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);

    &:last-child {
      border-left: 4px solid #00ffff;
    }

    .person-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;

      .person-name-row {
        display: flex;
        align-items: center;
        gap: 12px;

        .person-name {
          color: #ffffff;
          font-size: 28px;
          font-weight: 600;
          line-height: 1.4;
        }

        .person-relation {
          color: rgba(255, 255, 255, 0.7);
          font-size: 20px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }

      .person-summary {
        color: rgba(255, 255, 255, 0.7);
        font-size: 22px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        line-height: 1.4;
        cursor: text;
        transition: all 0.2s ease;
        user-select: text;

        // 默认只显示一行
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;

        // 展开状态
        &.expanded {
          display: block;
          -webkit-line-clamp: unset;
          line-clamp: unset;
          -webkit-box-orient: unset;
        }

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    .person-actions {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .edit-btn {
        background: transparent;
        border: 2px solid #00bcd4;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(0, 188, 212, 0.1);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
        }

        .edit-icon {
          width: 20px;
          height: 20px;
          filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
            contrast(96%);
        }
      }

      .delete-btn {
        background: transparent;
        border: 2px solid #ef4444;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(239, 68, 68, 0.1);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .delete-icon {
          width: 20px;
          height: 20px;
          filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg)
            brightness(104%) contrast(97%);
        }
      }
    }
  }
}

.person-footer {
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .add-person-btn {
    width: 100%;
    background: rgba(0, 188, 212, 0.1);
    backdrop-filter: blur(10px);
    color: #00bcd4;
    border: 2px solid #00bcd4;
    border-radius: 20px;
    padding: 16px 16px;
    font-size: 28px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    height: 60px;

    &:hover {
      background: rgba(0, 188, 212, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    .add-icon {
      font-size: 32px;
      font-weight: bold;
    }
  }
}

// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 500px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;

  &.edit-dialog {
    max-width: 600px;
    max-height: 800px;
    overflow-y: auto;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px;
    font-weight: 600;
  }

  .dialog-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;

  &.edit-content {
    max-height: 60vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .input-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 16px;
    padding: 20px;
    background: rgba(0, 188, 212, 0.05);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &.avatar-name-group {
      margin-bottom: 30px;

      .labels-row {
        display: flex;
        gap: 24px;
        margin-bottom: 12px;

        .avatar-label {
          width: 120px;
          text-align: left;
          flex-shrink: 0;
          padding-left: 18px;
        }

        .name-label {
          flex: 1;
        }
      }

      .content-row {
        display: flex;
        gap: 24px;
        align-items: flex-end;

        .avatar-section {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          flex-shrink: 0;
          width: 120px;

          .person-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(139, 69, 19, 0.8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

            &.clickable-avatar {
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                border-color: #00ffff;
                box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
                transform: scale(1.05);
              }
            }
          }
        }

        .name-section {
          flex: 1;

          .input-field {
            width: 100%;
          }
        }
      }
    }

    .input-label {
      color: white;
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .input-field {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(0, 188, 212, 0.3);
      border-radius: 20px;
      padding: 18px 22px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 22px;
      line-height: 1.6;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: #00bcd4;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.2);
      }
    }

    .textarea-field {
      width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(0, 188, 212, 0.3);
      border-radius: 20px;
      padding: 18px 22px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 22px;
      line-height: 1.6;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      resize: vertical;
      min-height: 100px;
      font-family: inherit;
      transition: all 0.3s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: #00bcd4;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.2);
      }
    }

    // 关键属性容器样式
    .key-attributes-container {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .attribute-item,
      .add-attribute-container {
        display: flex;
        gap: 12px;
        align-items: center;

        .attribute-key,
        .attribute-value {
          flex: 1;
          background: rgba(255, 255, 255, 0.05);
          border: none;
          border-radius: 12px;
          padding: 16px;
          color: rgba(255, 255, 255, 0.9);
          font-size: 20px;
          line-height: 1.6;
          box-sizing: border-box;
          backdrop-filter: blur(10px);
          transition: all 0.2s ease;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          &:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.3);
          }

          &:read-only {
            background: rgba(255, 255, 255, 0.02);
            color: rgba(255, 255, 255, 0.6);
          }
        }

        .attribute-key {
          max-width: 150px;
        }

        .remove-attribute-btn {
          background: transparent;
          border: none;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          color: rgba(255, 255, 255, 0.8);
          transition: all 0.3s ease;
          font-size: 20px;
          flex-shrink: 0;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            transform: scale(1.1);
          }
        }
      }

      .add-attribute-btn-container {
        margin-top: 16px;

        .add-attribute-btn {
          width: 100%;
          background: transparent;
          color: #00bcd4;
          border: 2px solid #00bcd4;
          border-radius: 20px;
          padding: 16px 16px;
          font-size: 28px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 60px;

          &:hover {
            background: rgba(0, 188, 212, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
          }
        }
      }
    }
  }

  .delete-warning {
    color: #ffffff;
    font-size: 32px;
    font-weight: 600;
    line-height: 1.4;
    margin: 0 0 12px 0;
    text-align: center;

    strong {
      color: #ef4444;
    }
  }

  .delete-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 24px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
  }
}

.dialog-footer {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .cancel-btn,
  .confirm-btn,
  .delete-confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 28px;
    font-weight: 600;
    cursor: pointer;
    border: 2px solid;
    background: transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    width: 200px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
    }
  }

  .confirm-btn {
    color: #00bcd4;
    border-color: #00bcd4;

    &:hover:not(:disabled) {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }
  }

  .delete-confirm-btn {
    color: #ef4444;
    border-color: #ef4444;

    &:hover:not(:disabled) {
      background: rgba(239, 68, 68, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
