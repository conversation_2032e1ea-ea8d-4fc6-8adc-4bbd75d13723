import { createRouter, createWebHistory, RouteLocationNormalized } from 'vue-router';
import Layout from '@/layout/index.vue';
import config from '@/config';
import { getGlobalLab, tracker } from '@/lib/LXAnalytics';
import ChatRoutes from './chat';

const router = createRouter({
  history: createWebHistory(config.baseRoute),
  routes: [
    {
      path: '/',
      component: Layout,
      redirect: 'chat',
      children: [...ChatRoutes],
    },
    {
      path: '/404',
      name: '404',
      component: () => import('@/pages/notFound/index.vue'),
    },
    {
      path: '/:pathMatch(.*)',
      redirect: '/404',
    },
  ],
});
router.afterEach((to: RouteLocationNormalized) => {
  if (to.meta.cid) {
    const labData = getGlobalLab();
    const pageCase = tracker(
      'pageView',
      labData,
      {},
      {
        cid: to.meta.cid,
      },
    );
    to.meta.pageCase = (type: string, bid: string, customData: Record<string, unknown> | null = {}) => {
      if (customData === null) {
        customData = {};
      }
      const { custom: globalCustomData } = getGlobalLab();
      pageCase(type, bid, {
        custom: { ...globalCustomData, ...customData },
      });
    };
  }
});
export default router;
